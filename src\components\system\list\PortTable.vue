<script setup lang="ts">
import { useI18n } from 'vue-i18n'

const emit = defineEmits(['update'])

const { t } = useI18n()

onMounted(() => {
  getPortList()
})

const portData: any = ref([])
async function getPortList() {
  const data = await $api('', { requestType: 267 })
  if (data.err_code == 0) {
    if (data.info.map_ip_port != undefined && data.info.map_ip_port != '')
      portData.value = data.info.map_ip_port
    else
      portData.value = []

    emit('update', portData.value) // 这里通知父组件
  }
}

// 显式暴露 getPortList 方法给父组件
defineExpose({
  getPortList, // 父组件可通过 ref 访问 this.$refs.child.getPortList()
})

const delItem = async (port_value: any, index: any) => {
  const data = await $api('', {
    requestType: 269,
    data: {
      port_num: port_value,
    },
  })

  portData.value.splice(index, 1)
  setTimeout(() => {
    getPortList()
  }, 3000)
}

const changeStatus = async (item: any, disable: any) => {
  const data = await $api('', {
    requestType: 268,
    data: {
      map_port_disable: disable,
      map_lan_port_ip: item.map_lan_ip,
      wan_pc_ip: item.wan_pc_ip,
      port_num: item.port_num,
    },
  })

  setTimeout(() => {
    getPortList()
  }, 1000)
}

const getPaddingStyle = (index: number) => index ? 'padding-block-end: 1.25rem;' : 'padding-block: 1.25rem;'
</script>

<template>
  <VCard>
    <VTable class="text-no-wrap transaction-table">
      <thead>
        <tr>
          <th>{{ t('NetworkConfig.Port.Status') }}</th>
          <th>{{ t('NetworkConfig.Port.DeviceIP') }}</th>
          <th>{{ t('NetworkConfig.Port.WANPCIP') }}</th>
          <th>{{ t('NetworkConfig.Port.Port') }}</th>
          <th>{{ t('NetworkConfig.Port.Actions') }}</th>
        </tr>
      </thead>

      <tbody>
        <tr
          v-for="(transition, index) in portData"
          :key="index"
        >
          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <div class="text-sm">
              <VChip
                v-if="transition.map_port_disable == 0"
                variant="tonal"
                color="success"
              >
                <div class="text-sm">
                  {{ t('NetworkConfig.Port.Enable') }}
                </div>
              </VChip>
              <VChip
                v-else
                variant="tonal"
                color="error"
              >
                <div class="text-sm">
                  {{ t('NetworkConfig.Port.Disable') }}
                </div>
              </VChip>
            </div>
          </td>
          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <div class="text-sm">
              {{ transition.map_lan_ip }}
            </div>
          </td>

          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <div class="text-sm">
              {{ transition.wan_pc_ip }}
            </div>
          </td>
          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <div class="text-sm">
              {{ transition.port_num }}
            </div>
          </td>
          <td
            :style="getPaddingStyle(index)"
            style="padding-inline-end: 1.5rem;"
          >
            <div class="flexBox">
              <VBtn
                v-if="transition.map_port_disable == 0"
                class="text-sm mr-2"
                color="secondary"
                variant="text"
                @click="changeStatus(transition, '1')"
              >
                {{ t('NetworkConfig.Port.Disable') }}
              </VBtn>
              <VBtn
                v-else
                class="text-sm  mr-2"
                color="success"
                variant="text"
                @click="changeStatus(transition, '0')"
              >
                {{ t('NetworkConfig.Port.Enable') }}
              </VBtn>
              <VBtn
                class="text-sm text-red"
                color="error"
                variant="text"
                @click="delItem(transition.port_num, index)"
              >
                {{ t('NetworkConfig.Port.Delete') }}
              </VBtn>
            </div>
          </td>
        </tr>
      </tbody>
    </VTable>
  </VCard>
</template>

<style lang="scss">
.transaction-table {
  &.v-table .v-table__wrapper > table > tbody > tr:not(:last-child) > td,
  &.v-table .v-table__wrapper > table > tbody > tr:not(:last-child) > th {
    border-block-end: none !important;
  }
}

.download {
  color: var(--Color-Primary-primary-500, #4080ff);

  /* Basic Typography/caption */
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.4px;
  line-height: 18px;

  /* 138.462% */
}

.upload {
  color: var(--Color-Success-success-500, #28c76f);

  /* Basic Typography/caption */
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  letter-spacing: 0.4px;
  line-height: 18px;

  /* 138.462% */
}

.flexBox {
  display: flex;
  align-items: center;
}

.text-red {
  color: #ff7074 !important;
}
</style>
