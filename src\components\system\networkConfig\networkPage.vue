<script lang="ts" setup>
import { getCurrentInstance } from 'vue'
import { useI18n } from 'vue-i18n'
import switchClosed from '@images/svg/switch-closed.svg'
import switchOpened from '@images/svg/switch-opened.svg'
import { isValidIPv4, isValidMacAddress } from '@layouts/utils'

const { t } = useI18n()

// 方式1：通过 globalProperties
const instance = getCurrentInstance()

const getWaittingModal = (type: any, url?: string) => {
  console.log('[Page] Calling show modal')
  instance?.proxy?.$showWaitingModal?.(type, url)
}

const timer: any = ref(null)

onMounted(() => {
  getAc(true)
  clearInterval(timer.value)
  timer.value = null
  timer.value = setInterval(() => {
    getAc(false)
  }, 15000)
  console.log('switchOpened', switchOpened)
})

onUnmounted(() => {
  clearInterval(timer.value)
  timer.value = null
})

const acInfoMac: any = reactive({})

const getAc = async (flag: boolean) => {
  const data = await $api('', { requestType: 200 })
  if (data.err_code == 0) {
    if (
      data.info.mode.systemMode != undefined
      && data.info.mode.systemMode != ''
    ) {
      if (
        data.info.wanInfo.wanMacAddress != undefined
        && data.info.wanInfo.wanMacAddress != ''
      )
        acInfoMac.mac = data.info.wanInfo.wanMacAddress
      else if (
        data.info.network.wan.wanMacAddress != undefined
        && data.info.network.wan.wanMacAddress != ''
      )
        acInfoMac.mac = data.info.network.wan.wanMacAddress

      sessionStorage.setItem('macAddress', acInfoMac.mac)
    }
  }
  getNetworkInfo(flag)
}

async function getNetworkInfo(flag: boolean) {
  const data = await $api('', { requestType: 201 })
  if (data.info.wan != undefined && data.info.wan != '') {
    if (
      data.info.wan.wanProtocol != undefined
      && data.info.wan.wanProtocol != ''
    ) {
      if (flag) {
        if (data.info.wan.wanProtocol == 'dhcp')
          radioGroup.value = 0

        if (data.info.wan.wanProtocol == 'pppoe')
          radioGroup.value = 1

        if (data.info.wan.wanProtocol == 'static')
          radioGroup.value = 2
      }
      if (
        data.info.wan.wanMacAddress != undefined
        && data.info.wan.wanMacAddress != ''
      )
        acInfoMac.mac = data.info.wan.wanMacAddress
      else if (
        sessionStorage.getItem('macAddress') != undefined
        && sessionStorage.getItem('macAddress') != ''
      )
        acInfoMac.mac = sessionStorage.getItem('macAddress')

      // pppoe 接收数据
      if (data.info.wan.wanProtocol == 'pppoe') {
        if (
          data.info.wan.wanUsername != undefined
          && data.info.wan.wanUsername != ''
        )
          portalTextArray[1].wanUsername = data.info.wan.wanUsername

        if (
          data.info.wan.wanPassword != undefined
          && data.info.wan.wanPassword != ''
        )
          portalTextArray[1].wanPassword = data.info.wan.wanPassword

        if (data.info.wan.wanAc != undefined && data.info.wan.wanAc != '')
          portalTextArray[1].wanAc = data.info.wan.wanAc

        if (
          data.info.wan.wanService != undefined
          && data.info.wan.wanService != ''
        )
          portalTextArray[1].wanService = data.info.wan.wanService
      }

      // static ip 接收数据
      if (data.info.wan.wanProtocol == 'static') {
        if (
          data.info.wan.wanIpAddress != undefined
          && data.info.wan.wanIpAddress != ''
        )
          portalTextArray[2].wanIpAddress = data.info.wan.wanIpAddress

        if (
          data.info.wan.wanGateway != undefined
          && data.info.wan.wanGateway != ''
        )
          portalTextArray[2].wanGateway = data.info.wan.wanGateway

        if (
          data.info.wan.wanNetmask != undefined
          && data.info.wan.wanNetmask != ''
        )
          portalTextArray[2].wanNetmask = data.info.wan.wanNetmask

        if (data.info.wan.wanDns != undefined && data.info.wan.wanDns != '') {
          data.info.wan.wanDns.forEach((dns: string, index: number) => {
            if (index === 0)
              portalTextArray[2].mainDns = dns

            if (index === 1)
              portalTextArray[2].backupDns = dns
          })
        }
      }
    }
  }
}

const portalTextArray: any = reactive([
  {
    title: t('NetworkConfig.Network.DynamicIP'),
    desc: t('NetworkConfig.Network.DynamicIPDesc'),
    toggleSwitch: false,
    mac: '',
  },
  {
    title: t('NetworkConfig.Network.PPPoE'),
    desc: t('NetworkConfig.Network.PPPoEDesc'),
    toggleSwitch: false,
    mac: '',
    wanUsername: '',
    wanPassword: '',
    wanAc: '',
    wanService: '',
  },
  {
    title: t('NetworkConfig.Network.StaticIP'),
    desc: t('NetworkConfig.Network.StaticIPDesc'),
    toggleSwitch: false,
    mac: '',
    wanIpAddress: '',
    wanGateway: '',
    wanNetmask: '',
    mainDns: '',
    backupDns: '',
  },
])

const radioGroup = ref(0)

const applyNetwork = async () => {
  networkForm.value.validate().then(async (result: any) => {
    if (!result.valid)
      return
    let data = {
      wan: {
        wanProtocol: 'dhcp',
        wanMacAddress: portalTextArray[0].mac,
      },
    }
    if (radioGroup.value == 1) {
      const reg = /^[\w~!@#$%^&*()+`\-=[\]{}<>;:|?,./]*$/
      if (!reg.test(portalTextArray[1].wanUsername)) {
        alert(t('NetworkConfig.Network.AccountIncorrect'))

        return false
      }
      if (!reg.test(portalTextArray[1].wanPassword)) {
        alert(t('NetworkConfig.Network.PasswordIncorrect'))

        return false
      }
      data = {
        wan: {
          wanProtocol: 'pppoe',
          wanMacAddress: portalTextArray[1].mac,
          wanUsername: portalTextArray[1].wanUsername,
          wanPassword: portalTextArray[1].wanPassword,
          wanAc: portalTextArray[1].wanAc,
          wanService: portalTextArray[1].wanService,
        },
      }
    }
    if (radioGroup.value == 2) {
      data = {
        wan: {
          wanProtocol: 'static',
          wanMacAddress: portalTextArray[2].mac,
          wanIpAddress: portalTextArray[2].wanIpAddress,
          wanGateway: portalTextArray[2].wanGateway,
          wanDns: [portalTextArray[2].mainDns, portalTextArray[2].backupDns],
          wanNetmask: portalTextArray[2].wanNetmask,
        },
      }
    }
    const res = await $api('', { requestType: 102, data })
    if (res.err_code == 0 || res.err_code == -14) {
      if (res.err_code == 0)
        getWaittingModal(5)
      else getWaittingModal(4)
    }
    else {
      getWaittingModal(9999)
    }
  })
}

const changeFlag = (index: number) => {
  portalTextArray[index].toggleSwitch = !portalTextArray[index].toggleSwitch
}

const networkForm = ref()

const validateAccountAndPassword = (value: string) => {
  const reg = /^[\w~!@#$%^&*()+`\-=[\]{}<>;:|?,./]*$/

  return reg.test(value)
}
</script>

<template>
  <VCard class="mb-5">
    <VCardText>
      <VForm ref="networkForm">
        <VRadioGroup v-model="radioGroup">
          <div
            v-for="(item, index) in portalTextArray"
            :key="item"
            :class="[radioGroup == index ? 'activeBorder mb-4' : 'border mb-4']"
          >
            <div class="pa-4">
              <VRadio
                :label="`${item.title}`"
                :value="index"
                color="primary"
                density="comfortable"
                class="mb-2"
              />
              <div class="desc indent38">
                {{ item.desc }}
              </div>
              <div
                v-if="radioGroup == index"
                class="formBox"
              >
                <div v-if="radioGroup == 0">
                  <div class="v-label mb-2 text-body-2 text-wrap">
                    {{ t('NetworkConfig.Network.MACClone') }}
                  </div>
                  <div
                    class="desc mb-2"
                    @click.stop.prevent
                  >
                    <span>{{ t('NetworkConfig.Network.MACCloneDesc') }}</span>
                    <div @click="changeFlag(index)">
                      <VIcon
                        style="inline-size: 50px;"
                        :icon="item.toggleSwitch ? switchOpened : switchClosed"
                      />
                    </div>
                  </div>
                  <div class="v-label mb-1 text-body-2 text-wrap">
                    {{ t('NetworkConfig.Network.CurrentMAC') }}
                  </div>
                  <div class="desc mb-2">
                    {{ acInfoMac.mac }}
                  </div>
                  <AppTextField
                    v-if="item.toggleSwitch"
                    v-model="item.mac"
                    class="mb-2"
                    :label="t('NetworkConfig.Network.CloneMAC')"
                    :placeholder="t('NetworkConfig.Network.EnterCloneMAC')"
                    :rules="[
                      (v:string) => !!v || t('NetworkConfig.Network.CloneMACRequired'),
                      (v:string) =>
                        isValidMacAddress(v) ? true : t('NetworkConfig.Network.CloneMACInvalid'),
                    ]"
                  />
                </div>
                <div v-if="radioGroup == 1">
                  <AppTextField
                    v-model="item.wanUsername"
                    class="mb-4"
                    :label="t('NetworkConfig.Network.InternetAccount')"
                    :placeholder="t('NetworkConfig.Network.EnterInternetAccount')"
                    :rules="[
                      (v:string) => !!v || t('NetworkConfig.Network.InternetAccountRequired'),
                      (v:string) => validateAccountAndPassword(v) ? true : t('NetworkConfig.Network.InternetAccountInvalid'),
                    ]"
                  />
                  <AppTextField
                    v-model="item.wanPassword"
                    class="mb-4"
                    :label="t('NetworkConfig.Network.InternetPassword')"
                    :placeholder="t('NetworkConfig.Network.EnterInternetPassword')"
                    :rules="[
                      (v:string) => !!v || t('NetworkConfig.Network.InternetPasswordRequired'),
                      (v:string) => validateAccountAndPassword(v) ? true : t('NetworkConfig.Network.InternetPasswordInvalid'),
                    ]"
                  />
                  <div class="v-label mb-2 text-body-2 text-wrap">
                    {{ t('NetworkConfig.Network.MACClone') }}
                  </div>
                  <div
                    class="desc mb-2"
                    @click.stop.prevent
                  >
                    <span>{{ t('NetworkConfig.Network.MACCloneDesc') }}</span>
                    <div @click="changeFlag(index)">
                      <VIcon
                        style="inline-size: 50px;"
                        :icon="item.toggleSwitch ? switchOpened : switchClosed"
                      />
                    </div>
                  </div>
                  <div class="v-label mb-1 text-body-2 text-wrap">
                    {{ t('NetworkConfig.Network.CurrentMAC') }}
                  </div>
                  <div class="desc mb-2">
                    {{ acInfoMac.mac }}
                  </div>
                  <AppTextField
                    v-if="item.toggleSwitch"
                    v-model="item.mac"
                    class="mb-2"
                    :label="t('NetworkConfig.Network.CloneMAC')"
                    :placeholder="t('NetworkConfig.Network.EnterCloneMAC')"
                    :rules="[
                      (v:string) => !!v || t('NetworkConfig.Network.CloneMACRequired'),
                      (v:string) =>
                        isValidMacAddress(v) ? true : t('NetworkConfig.Network.CloneMACInvalid'),
                    ]"
                  />
                </div>
                <div v-if="radioGroup == 2">
                  <AppTextField
                    v-model="item.wanIpAddress"
                    class="mb-4"
                    :label="t('NetworkConfig.Network.IPAddress')"
                    :placeholder="t('NetworkConfig.Network.EnterIPAddress')"
                    :rules="[
                      (v:string) => !!v || t('NetworkConfig.Network.IPAddressRequired'),
                      (v:string) => isValidIPv4(v) ? true : t('NetworkConfig.Network.IPAddressInvalid'),
                    ]"
                  />
                  <AppTextField
                    v-model="item.wanNetmask"
                    class="mb-4"
                    :label="t('NetworkConfig.Network.Subnet')"
                    :placeholder="t('NetworkConfig.Network.EnterSubnet')"
                    :rules="[
                      (v:string) => !!v || t('NetworkConfig.Network.SubnetRequired'),
                      (v:string) => isValidIPv4(v) ? true : t('NetworkConfig.Network.SubnetInvalid'),
                    ]"
                  />
                  <AppTextField
                    v-model="item.wanGateway"
                    class="mb-4"
                    :label="t('NetworkConfig.Network.Gateway')"
                    :placeholder="t('NetworkConfig.Network.EnterGateway')"
                    :rules="[
                      (v:string) => !!v || t('NetworkConfig.Network.GatewayRequired'),
                      (v:string) => isValidIPv4(v) ? true : t('NetworkConfig.Network.GatewayInvalid'),
                    ]"
                  />
                  <div class="mb-4 d-flex">
                    <AppTextField
                      v-model="item.mainDns"
                      class="mr-2"
                      :label="t('NetworkConfig.Network.PrimaryDNS')"
                      :placeholder="t('NetworkConfig.Network.EnterPrimaryDNS')"
                    />
                    <AppTextField
                      v-model="item.backupDns"
                      :label="t('NetworkConfig.Network.SecondaryDNS')"
                      :placeholder="t('NetworkConfig.Network.EnterSecondaryDNS')"
                    />
                  </div>
                  <div class="blueArea mb-2">
                    <VIcon icon="tabler-info-circle" />
                    <div>
                      <p class="topText">
                        {{ t('NetworkConfig.Network.RecommendedDNS') }}
                      </p>
                    </div>
                  </div>
                  <div class="v-label mb-2 text-body-2 text-wrap">
                    {{ t('NetworkConfig.Network.MACClone') }}
                  </div>
                  <div
                    class="desc mb-4"
                    @click.stop.prevent
                  >
                    <span>{{ t('NetworkConfig.Network.MACCloneDesc') }}</span>
                    <div @click="changeFlag(index)">
                      <VIcon
                        style="inline-size: 50px;"
                        :icon="item.toggleSwitch ? switchOpened : switchClosed"
                      />
                    </div>
                  </div>
                  <div class="v-label mb-1 text-body-2 text-wrap">
                    {{ t('NetworkConfig.Network.CurrentMAC') }}
                  </div>
                  <div class="desc mb-4">
                    {{ acInfoMac.mac }}
                  </div>
                  <AppTextField
                    v-if="item.toggleSwitch"
                    v-model="item.mac"
                    :label="t('NetworkConfig.Network.CloneMAC')"
                    :placeholder="t('NetworkConfig.Network.EnterCloneMAC')"
                    :rules="[
                      (v:string) => !!v || t('NetworkConfig.Network.CloneMACRequired'),
                      (v:string) =>
                        isValidMacAddress(v) ? true : t('NetworkConfig.Network.CloneMACInvalid'),
                    ]"
                  />
                </div>
              </div>
            </div>
          </div>
          <div style="display: flex; justify-content: flex-end;">
            <VBtn
              color="primary"
              @click="applyNetwork"
            >
              {{ t('NetworkConfig.Network.SaveSettings') }}
            </VBtn>
          </div>
        </VRadioGroup>
      </VForm>
    </VCardText>
  </VCard>
</template>

<style lang="scss">
.v-radio {
  .v-selection-control {
    opacity: 1 !important;
  }

  .v-selection-control__wrapper {
    opacity: 1 !important;
  }

  .v-selection-control__input {
    opacity: 1 !important;
  }
}

.activeBorder {
  border: 1px solid rgb(var(--v-theme-primary));
  border-radius: 6px;
}

.border {
  border: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
  border-radius: 6px;
}

.desc {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--Light-Text-Secondary, text-secondary);

  /* Basic Typography/body-1 */
  font-family: "PingFang SC";
  font-feature-settings: "liga" off, "clig" off;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 146.667% */
}

.indent38 {
  margin-inline-start: 38px;
}

.formBox {
  padding-inline-start: 38px;
}

.blueArea {
  display: flex;
  border: 1px solid rgb(22, 93, 255);
  border-radius: 6px;
  background: var(--Opacity-Color-Primary-primary-8, rgba(22, 93, 255, 8%));
  padding-block: 16px;
  padding-inline: 15px;

  p {
    color: var(--Color-Primary-primary-800, #072ca6);
    font-family: "PingFang SC";
    font-feature-settings: "liga" off, "clig" off;
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px; /* 160% */
    margin-block-end: 0;
    margin-inline-start: 5px;
  }
}
</style>
