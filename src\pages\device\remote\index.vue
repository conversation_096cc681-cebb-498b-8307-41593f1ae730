<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

interface DeviceInfo {
  sn: string
  model: string
  mac: string
  onoff: string
  runtime: string
  uptime: string
  user_name: string
  ip: string
  version: string
}
const apModel = ref('0')
const apSn = ref('')
const route = useRoute()
let mac = ''
onMounted(() => {
  if (route.query.mac) {
    mac = route.query.mac as string
    apModel.value = route.query.mode as string
    apSn.value = route.query.sn as string
    getDeviceInfo()
  }
})

const deviceInfo = ref({} as DeviceInfo)

function getDeviceInfo() {
  $api('', {
    requestType: 530,
    data: {
      mac,
    },
  }).then(res => {
    if (res.err_code === 0)
      deviceInfo.value = res.info
    else
      ElMessage.error(res.msg || t('Device.Remote.GetDeviceInfoFailed'))
  })
}

const saveMode = () => {
  $api('', {
    requestType: 504,
    data: {
      net_type: apModel.value,
      ap_sn_list: `${apSn.value} `,
    },
  }).then(res => {
    if (res.err_code === 0)
      getDeviceInfo()
    else
      ElMessage.error(res.msg || t('Device.Remote.ModeSwitchFailed'))
  })
}

const router = useRouter()

const backPage = () => {
  router.back()
}
</script>

<template>
  <div>
    <VCard class="pa-4 mb-4">
      <div class="text-h5 mb-2">
        AP310i
      </div>
      <div class="bg-grey-light pa-4 rounded">
        <div class="text-h6 mb-4">
          {{ t('Device.Remote.DeviceInfo') }}
        </div>
        <VRow>
          <VCol>
            <div class="mb-1 text-subtitle-2">
              {{ t('Device.Remote.Status') }}
            </div>
            <div
              v-if="deviceInfo.onoff == 'online'"
              class="text-success text-subtitle-1 font-weight-medium font-highlight"
            >
              {{ t('Device.AP.Online') }}
            </div>
            <div
              v-else
              class="text-error text-subtitle-1 font-weight-medium font-highlight"
            >
              {{ t('Device.AP.Offline') }}
            </div>
          </VCol>
          <VCol>
            <div class="mb-1 text-subtitle-2">
              {{ t('Device.Remote.DeviceType') }}
            </div>
            <div class="text-subtitle-1 font-weight-medium font-highlight">
              {{ deviceInfo.model }}
            </div>
          </VCol>
          <VCol>
            <div class="mb-1 text-subtitle-2">
              {{ t('Device.AP.FirmwareVersion') }}
            </div>
            <div class="text-subtitle-1 font-weight-medium font-highlight">
              {{ deviceInfo.version }}
            </div>
          </VCol>
          <VCol>
            <div class="mb-1 text-subtitle-2">
              {{ t('Device.Remote.IPAddress') }}
            </div>
            <div class="text-subtitle-1 font-weight-medium font-highlight">
              {{ deviceInfo.ip }}
            </div>
          </VCol>
        </VRow>
        <VRow>
          <VCol>
            <div class="mb-1 text-subtitle-2">
              {{ t('Device.Remote.MACAddress') }}
            </div>
            <div class="text-subtitle-1 font-weight-medium font-highlight">
              {{ deviceInfo.mac }}
            </div>
          </VCol>
          <VCol>
            <div class="mb-1 text-subtitle-2">
              {{ t('Device.Remote.OnlineTime') }}
            </div>
            <div class="text-subtitle-1 font-weight-medium font-highlight">
              {{ deviceInfo.uptime }}
            </div>
          </VCol>
          <VCol>
            <div class="mb-1 text-subtitle-2">
              {{ t('Device.Remote.RunningTime') }}
            </div>
            <div class="text-subtitle-1 font-weight-medium font-highlight">
              {{ deviceInfo.runtime }}
            </div>
          </VCol>
          <VCol />
        </VRow>
      </div>
    </VCard>

    <VCard class="pa-4">
      <div class="text-h5 mb-4">
        {{ t('Device.Remote.ModeSwitch') }}
      </div>
      <div>
        <VRadioGroup v-model="apModel">
          <div
            class="rounded pa-5 d-flex align-start mb-4"
            :class="[apModel === '0' ? 'border-fill-primary' : 'border border-fill-grey']"
          >
            <div>
              <VRadio
                color="primary"
                value="0"
              />
            </div>
            <div class="ml-2">
              <div class="text-h6 d-flex align-center mb-2">
                <VIcon
                  :color="apModel == '0' ? 'primary' : 'secondary'"
                  class="mr-2"
                  icon="tabler-router"
                />
                {{ t('Device.Remote.RouterMode') }}
              </div>
              <div class="text-subtitle-2 text-secondary">
                {{ t('Device.Remote.RouterModeDesc') }}
              </div>
            </div>
          </div>
          <div
            class="rounded pa-5 d-flex align-start mb-4"
            :class="[apModel === '1' ? 'border-fill-primary' : 'border border-fill-grey']"
          >
            <div>
              <VRadio
                color="primary"
                value="1"
              />
            </div>
            <div class="ml-2">
              <div class="text-h6 d-flex align-center mb-2">
                <VIcon
                  :color="apModel == '1' ? 'primary' : 'secondary'"
                  class="mr-2"
                  icon="tabler-rss"
                />
                {{ t('Device.Remote.APMode') }}
              </div>
              <div class="text-subtitle-2 text-secondary">
                {{ t('Device.Remote.APModeDesc') }}
              </div>
            </div>
          </div>
        </VRadioGroup>
      </div>
      <div class="d-flex justify-end mt-4">
        <VBtn
          class="mr-2"
          color="secondary"
          @click="backPage"
        >
          {{ t('Device.Remote.Cancel') }}
        </VBtn>
        <VBtn
          color="primary"
          @click="saveMode"
        >
          {{ t('Device.Remote.SaveSettings') }}
        </VBtn>
      </div>
    </VCard>
  </div>
</template>

<style lang="scss">
.font-highlight {
  color: rgba(47, 43, 61, 90%);
}
</style>
