<script lang="ts" setup>
import { ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { PerfectScrollbar } from 'vue3-perfect-scrollbar'
import { useTheme } from 'vuetify'
import { hexToRgb } from '@layouts/utils'
import { NETWORK_EVENT_LEVEL, NETWORK_EVENT_LEVEL_FILTER } from '@/utils/constants'

const { t } = useI18n()

const EVENT_STATUS_LIST = computed(() => {
  return EVENT_STATUS.map(item => ({
    ...item,
    label: t(item.label),
  }))
})

const eventTypesFilter = computed(() => {
  return NETWORK_EVENT_LEVEL_FILTER.map(item => ({
    ...item,
    title: t(item.title),
  }))
})

const vuetifyTheme = useTheme()

const cupUseChartConfig = computed(() => {
  return {
    chart: {
      type: 'area',
      parentHeightOffset: 0,
      zoom: { enabled: false },
      toolbar: { show: false },
    },
    annotations: {
      texts: [{
        x: 0,
        y: 28,
        text: t('NetworkDeviceDetail.CPUTitle'),
        fontSize: '18px',
        fontWeight: 500,
        foreColor: `rgba(${hexToRgb(vuetifyTheme.current.value.colors['on-surface'])}, 0.9)`,
      }, {
        x: 0,
        y: 56,
        text: t('NetworkDeviceDetail.CPUSubtitle'),
        fontSize: '15px',
        foreColor: `rgba(${hexToRgb(vuetifyTheme.current.value.colors['on-surface'])}, 0.55)`,
      }],
    },
    dataLabels: { enabled: false },
    colors: ['#00BAD1'],
    stroke: {
      curve: 'straight',
      width: 1.5,
      lineCap: 'round',
    },
    legend: {
      position: 'top',
      horizontalAlign: 'right',
      showForSingleSeries: true,
      formatter(seriesName: string, opts: any) {
        return `<span class="text-info ml-2">${seriesName}</span>`
      },
      itemMargin: {
        horizontal: 5,
        vertical: 0,
      },
      markers: {
        size: 4,
      },
      onItemClick: {
        toggleDataSeries: false,
      },

    },
    fill: {
      type: 'gradient',
      gradient: {
        type: 'vertical',
        opacityFrom: 0.3,
        opacityTo: 0.3,
        stops: [0, 100],
      },
    },
    grid: {
      padding: { top: 38 },
      strokeDashArray: 4,
      xaxis: {
        lines: { show: true },
      },
    },
    markers: {
      size: 0,
      strokeWidth: 0,
      strokeOpacity: 1,
      hover: {
        size: 3,
      },
    },
    xaxis: {
      axisBorder: { show: false },
      crosshairs: {
        stroke: {
          show: true,
          width: 1,
          color: '#B6B6B6',
          dashArray: 0,
        },
      },
      categories: cpuStatistics.timeLine,
    },
  }
})

const cupUsageSeries = computed(() => {
  return [{
    name: t('NetworkDeviceDetail.CPUTitle'),
    data: cpuStatistics.history,
  }]
})

const memoryUseChartConfig = computed(() => {
  return {
    chart: {
      type: 'area',
      parentHeightOffset: 0,
      zoom: { enabled: false },
      toolbar: { show: false },
    },
    annotations: {
      texts: [{
        x: 0,
        y: 28,
        text: t('NetworkDeviceDetail.MemoryTitle'),
        fontSize: '18px',
        fontWeight: 500,
        foreColor: `rgba(${hexToRgb(vuetifyTheme.current.value.colors['on-surface'])}, 0.9)`,
      }, {
        x: 0,
        y: 56,
        text: t('NetworkDeviceDetail.MemorySubtitle'),
        fontSize: '15px',
        foreColor: `rgba(${hexToRgb(vuetifyTheme.current.value.colors['on-surface'])}, 0.55)`,
      }],
    },
    dataLabels: { enabled: false },
    colors: ['#FF9F43'],
    stroke: {
      curve: 'straight',
      width: 1.5,
      lineCap: 'round',
    },
    legend: {
      position: 'top',
      horizontalAlign: 'right',
      showForSingleSeries: true,
      formatter(seriesName: string, opts: any) {
        return `<span class="text-warning ml-2">${seriesName}</span>`
      },
      itemMargin: {
        horizontal: 5,
        vertical: 0,
      },
      markers: {
        size: 4,
      },
      onItemClick: {
        toggleDataSeries: false,
      },

    },
    fill: {
      type: 'gradient',
      gradient: {
        type: 'vertical',
        opacityFrom: 0.3,
        opacityTo: 0.3,
        stops: [0, 100],
      },
    },

    // tooltip: {
    //   custom(data: any) {
    //     return `<div class='bar-chart pa-2'>
    //       <div>${data.series[0][data.dataPointIndex]}</div>
    //     </div>`
    //   },
    // },
    grid: {
      padding: { top: 38 },
      strokeDashArray: 4,
      xaxis: {
        lines: { show: true },
      },
    },
    markers: {
      size: 0,
      strokeWidth: 0,
      strokeOpacity: 1,

      // strokeColors: ['#24B364', '#E64449'],
      // colors: ['#fff', '#fff'],
      hover: {
        size: 3,
      },
    },
    xaxis: {
      axisBorder: { show: false },
      crosshairs: {
        stroke: {
          show: true,
          width: 1,
          color: '#B6B6B6',
          dashArray: 0,
        },
      },
      categories: memoryStatistics.timeLine,
    },
  }
})

const memoryUsageSeries = computed(() => {
  return [{
    name: t('NetworkDeviceDetail.MemoryTitle'),
    data: memoryStatistics.history,
  }]
})

const productsData = ref({
  total: 0,
  products: [],
})

const headers = [
  { title: t('NetworkDeviceDetail.EventLevel'), key: 'severity' },
  { title: t('NetworkDeviceDetail.EventType'), key: 'type' },
  { title: t('NetworkDeviceDetail.EventName'), key: 'event_name' },
  { title: t('NetworkDeviceDetail.OccurrenceTime'), key: 'timestamp' },
  { title: t('NetworkDeviceDetail.EventStatus'), key: 'status' },
  { title: t('NetworkDeviceDetail.Action'), key: 'detail' },
]

const eventClass = {
  0: 'default',
  1: 'info',
  2: 'warning',
  3: 'error',
}

const totalProduct = computed(() => productsData.value.products.length)
const page = ref(1)
const itemsPerPage = ref(10)

// 添加分页处理计算属性
const paginatedProducts = computed(() => {
  const start = (page.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value

  return productsData.value.products.slice(start, end)
})

const drawer = ref(false)

const isDialogVisible = ref(false)

const noMoreRemindHeaders = [{
  title: t('NetworkDeviceDetail.NO'),
  key: 'no',
}, {
  title: t('NetworkDeviceDetail.EventType'),
  key: 'device_type',
}, {
  title: t('NetworkDeviceDetail.EventName'),
  key: 'event_name',
}, {
  title: t('NetworkDeviceDetail.Action'),
  key: 'handle',
}]

interface DeviceInfo {
  sn: string
  model: string
  mac: string
  onoff: string
  runtime: string
  uptime: string
  user_name: string
  ip: string
  version: string
}

const deviceInfo = ref<DeviceInfo>({
  sn: '',
  model: '',
  mac: '',
  onoff: '',
  runtime: '',
  uptime: '',
  user_name: '',
  ip: '',
  version: '',
})

const route = useRoute()
let mac = ''
onMounted(() => {
  route.query.mac && (mac = route.query.mac as string)
  if (mac) {
    getDeviceInfo()
    getDeviceCpuStatistics()
    getDeviceMemoryStatistics()
    getDeviceEventList()
  }
})

const originalProducts = ref([])

const getDeviceEventList = () => {
  $api('', {
    requestType: 537,
    data: {
      mac,
    },
  }).then((res => {
    const list = res.info.ap_events

    // 筛选对象数组mac 等于mac的
    const filteredList = list.filter((item: any) => item.mac === mac)

    originalProducts.value = filteredList

    getIgnoreList()
  }))
}

function getDeviceInfo() {
  $api('', {
    requestType: 530,
    data: { mac },
  }).then((res => {
    if (res.err_code === 0)
      deviceInfo.value = res.info
    else
      ElMessage.error(t('Device.Remote.GetDeviceInfoFailed'))
  }))
}

let cpuStatistics = reactive({
  origin: [],
  timeLine: [] as string[],
  history: [] as number[],
})

const cpuAverage = computed(() => {
  if (cpuStatistics.history.length === 0)
    return 0
  const total = cpuStatistics.history.reduce((sum, value) => sum + value, 0)

  return (total / cpuStatistics.history.length).toFixed(2)
})

function getDeviceCpuStatistics() {
  $api('', {
    requestType: 531,
    data: { mac },
  }).then((res => {
    if (res.err_code === 0) {
      let list = res.info.cpu_history || []
      list = list.reverse()

      // 处理CPU统计数据
      cpuStatistics.origin = list
      cpuStatistics.timeLine = reactive([])
      cpuStatistics.history = reactive([])
      cpuStatistics.origin.forEach((item: any) => {
        cpuStatistics.timeLine.push(item.timestamp.split(' ')[1])
        cpuStatistics.history.push(item.usage_percent)
      })
    }
    else {
      ElMessage.error(t('GetDeviceCPUStatisticsFailed'))
    }
  }))
}

let memoryStatistics = reactive({
  origin: [],
  timeLine: [] as string[],
  history: [] as number[],
})

const memoryAverage = computed(() => {
  if (memoryStatistics.history.length === 0)
    return 0
  const total = memoryStatistics.history.reduce((sum, value) => sum + value, 0)

  return (total / memoryStatistics.history.length).toFixed(2)
})

function getDeviceMemoryStatistics() {
  $api('', {
    requestType: 532,
    data: { mac },
  }).then((res => {
    if (res.err_code === 0) {
      let list = res.info.memory_history || []
      list = list.reverse()

      // 处理内存统计数据
      memoryStatistics.origin = list
      memoryStatistics.timeLine = reactive([])
      memoryStatistics.history = reactive([])
      memoryStatistics.origin.forEach((item: any) => {
        memoryStatistics.timeLine.push(item.timestamp.split(' ')[1])
        memoryStatistics.history.push(item.usage_percent)
      })
    }
    else {
      ElMessage.error(t('GetDeviceMemoryStatisticsFailed'))
    }
  }))
}

const getEventIndex = (item: any) => {
  const str = item.severity

  // Critical/High/Medium/Minor
  if (str == 'Critical')
    return 3
  else if (str == 'High')
    return 2
  else if (str == 'Medium')
    return 1
  else if (str == 'Minor')
    return 0
}

const getEventName = (item: any) => {
  const index = getEventIndex(item)
  if (index >= 0 && index < NETWORK_EVENT_LEVEL.length)
    return t(NETWORK_EVENT_LEVEL[index].title)

  return t('Unknown')
}

const formatTime = (time: string) => {
  return moment(time).format('YYYY-MM-DD HH:mm:ss')
}

const eventDetail = ref({})

const showDetail = (item: any) => {
  drawer.value = true
  eventDetail.value = item
}

const markResolved = (item: any) => {
  $api('', {
    requestType: 524,
    data: {
      event_id: item.event_id,
      status: '2',
    },
  }).then((res => {
    if (res.err_code === 0)
      getDeviceEventList()
  }))
}

const markIgnored = (item: any) => {
  $api('', {
    requestType: 540,
    data: {
      mac: item.mac,
      event_name: item.event_name,
    },
  }).then((res => {
    if (res.err_code === 0)
      getDeviceEventList()
  }))
}

const markIgnoredChange = (item: any) => {
  isDialogVisible.value = false
  ElMessageBox.alert(t('NetworkDeviceDetail.ConfirmRemove'), t('NetworkDeviceDetail.Tip'), {
    confirmButtonText: t('NetworkDeviceDetail.Confirm'),
    cancelButtonText: t('NetworkDeviceDetail.Cancel'),
    type: 'warning',
  }).then(() => {
    $api('', {
      requestType: 541,
      data: {
        mac,
        event_name: item.event_name,
      },
    }).then((res => {
      if (res.err_code === 0) {
        setTimeout(() => {
          getIgnoreList()
        }, 2000)
      }
    }))
  })
}

const dealMethod = ref(-1)
const dealEvent = ref(-1)
const filterList = ref([])

watch(
  [() => dealMethod.value, () => dealEvent.value, () => originalProducts.value, () => filterList.value],
  ([newDealMethod, newDealEvent, newOriginalProducts, newFilterList]) => {
    console.log('filter', newDealMethod, newDealEvent, newOriginalProducts, newFilterList)
    let filteredProducts = []
    if (newDealEvent == -1) {
      if (newDealMethod == -1) {
        filteredProducts = newOriginalProducts
      }
      else {
        filteredProducts = newOriginalProducts.filter(product => {
          // 根据实际数据结构调整过滤条件
          return product.status == newDealMethod
        })
      }
    }
    else {
      if (newDealMethod == -1) {
        filteredProducts = newOriginalProducts.filter(product => {
          // 根据实际数据结构调整过滤条件
          return getEventIndex(product) == newDealEvent
        })
      }
      else {
        filteredProducts = newOriginalProducts.filter(product => {
          return product.status == newDealMethod && getEventIndex(product) == newDealEvent
        })
      }
    }

    const excludeEventNames = filterList.value.map(String)

    productsData.value.products = filteredProducts.filter(product =>
      !excludeEventNames.includes(product.event_name),
    )
  },
  { immediate: true, deep: true },
)

const ignoreList = ref([])

const getIgnoreList = () => {
  $api('', {
    requestType: 542,
    data: {
      mac,
    },
  }).then((res => {
    if (res.err_code === 0) {
      const arr = res.info.blocked_events

      filterList.value = arr
      console.log('getIgnoreList', res.info.blocked_events, originalProducts.value)

      // 筛选originalProducts.value  中event_name 中在arr中存在 ，arr 是event_name的数组
      const list = originalProducts.value.filter(item => {
        return arr.includes(item.event_name * 1)
      })

      //  list 中提取device_type 和event_name，组成新对象，加入新数组，重复的不添加

      const uniqueEvents = []

      list.forEach(event => {
        const { device_type, event_name } = event

        const existingEntry = uniqueEvents.find(entry =>
          entry.device_type === device_type && entry.event_name === event_name,
        )

        if (!existingEntry)
          uniqueEvents.push({ device_type, event_name })
      })
      ignoreList.value = uniqueEvents

      // 的device_type 和event_name 组成新数组 重复的不再添加
    }
  }))
}

const openIgnore = () => {
  isDialogVisible.value = true
}
</script>

<template>
  <div class="device-detail">
    <VCard class="mb-6">
      <template #title>
        <div class="mr-2 text-h5">
          {{ deviceInfo.usr_name }}
        </div>
      </template>
      <VCardText>
        <div class="bg-grey-light pa-4">
          <div class="text-button mb-2">
            {{ t('NetworkDeviceDetail.TerminalInfo') }}
          </div>
          <VRow>
            <VCol>
              <div class="label">
                {{ t('NetworkDeviceDetail.Status') }}
              </div>
              <div
                v-if="deviceInfo.onoff === 'online'"
                class="value text-success"
              >
                {{ t('NetworkDeviceDetail.Online') }}
              </div>
              <div
                v-else
                class="value text-error"
              >
                {{ t('NetworkDeviceDetail.Offline') }}
              </div>
            </VCol>
            <VCol>
              <div class="label">
                {{ t('NetworkDeviceDetail.Model') }}
              </div>
              <div class="value">
                {{ deviceInfo.model }}
              </div>
            </VCol>
            <VCol>
              <div class="label">
                {{ t('NetworkDeviceDetail.Version') }}
              </div>
              <div class="value">
                {{ deviceInfo.version }}
              </div>
            </VCol>
            <VCol>
              <div class="label">
                {{ t('NetworkDeviceDetail.IPAddress') }}
              </div>
              <div class="value">
                {{ deviceInfo.ip }}
              </div>
            </VCol>
          </VRow>
          <VRow>
            <VCol>
              <div class="label">
                {{ t('NetworkDeviceDetail.MACAddress') }}
              </div>
              <div class="value">
                {{ deviceInfo.mac }}
              </div>
            </VCol>
            <VCol>
              <div class="label">
                {{ t('NetworkDeviceDetail.Uptime') }}
              </div>
              <div class="value">
                {{ deviceInfo.uptime }}
              </div>
            </VCol>
            <VCol>
              <div class="label">
                {{ t('NetworkDeviceDetail.Runtime') }}
              </div>
              <div class="value">
                {{ deviceInfo.runtime }}
              </div>
            </VCol>
            <VCol />
          </VRow>
        </div>
      </VCardText>
    </VCard>

    <VRow class="mb-6">
      <VCol>
        <VCard>
          <VCardText>
            <VueApexCharts
              class="custom-area-chart mb-4"
              type="area"
              height="260"
              :options="cupUseChartConfig"
              :series="cupUsageSeries"
            />
            <div class="pa-4 bg-grey-light rounded">
              <div class="d-flex align-end justify-space-between mb-2">
                <div class="text-info text-h3">
                  {{ cpuAverage }}%
                </div>
                <div class="text-body-2	">
                  {{ t('NetworkDeviceDetail.CPUAvg') }}
                </div>
              </div>
              <VProgressLinear
                v-model="cpuAverage"
                class="rounded-progress"
                :rounded="false"
                color="info"
                height="20"
                bg-color="info"
                :bg-opacity="0.08"
              />
            </div>
          </VCardText>
        </VCard>
      </VCol>
      <VCol>
        <VCard>
          <VCardText>
            <VueApexCharts
              class="custom-area-chart mb-4"
              type="area"
              height="260"
              :options="memoryUseChartConfig"
              :series="memoryUsageSeries"
            />
            <div class="pa-4 bg-grey-light rounded">
              <div class="d-flex align-end justify-space-between mb-2">
                <div class="text-warning text-h3">
                  {{ memoryAverage }}%
                </div>
                <div class="text-body-2	">
                  {{ t('NetworkDeviceDetail.MemoryAvg') }}
                </div>
              </div>
              <VProgressLinear
                v-model="memoryAverage"
                class="rounded-progress"
                :rounded="false"
                color="warning"
                height="20"
                bg-color="warning"
                :bg-opacity="0.08"
              />
            </div>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <VCard class="mb-6">
      <template #title>
        <div class="d-flex align-start justify-space-between">
          <div class="d-flex align-center">
            <div class="mr-4 text-h5">
              {{ t('NetworkDeviceDetail.EventList') }}
            </div>
            <BtnGroupSelector
              v-model:value="dealMethod"
              class="mr-4"
              :options="EVENT_STATUS_LIST"
            />
            <BtnGroupSelector
              v-model:value="dealEvent"
              :options="eventTypesFilter"
              item-title="title"
            />
          </div>
          <VBtn
            variant="tonal"
            color="secondary"
            @click="openIgnore"
          >
            {{ t('NetworkDeviceDetail.NoMoreRemind') }}
            <span v-if="ignoreList.length > 0">({{ ignoreList.length }})</span>
          </VBtn>
        </div>
      </template>
      <VDivider />
      <VDataTableServer
        :items="paginatedProducts"

        :headers="headers"
        :items-length="totalProduct"
        :no-data-text="t('NoData')"
      >
        <template #item.type="{ item }">
          <div v-if="item.type == '0'">
            {{ t('NetworkDeviceDetail.DeviceAccess') }}
          </div>
          <div v-if="item.type == '1'">
            {{ t('NetworkDeviceDetail.DevicePerformance') }}
          </div>
        </template>
        <template #item.event_name="{ item }">
          <div v-if="item.event_name == '0'">
            {{ t('NetworkDeviceDetail.APOnline') }}
          </div>
          <div v-if="item.event_name == '1'">
            {{ t('NetworkDeviceDetail.APOffline') }}
          </div>
          <div v-if="item.event_name == '3'">
            {{ t('NetworkDeviceDetail.APHighTemp') }}
          </div>
        </template>
        <template #item.severity="{ item }">
          <VChip
            variant="outlined"
            :color="eventClass[getEventIndex(item)]"
          >
            {{ getEventName(item) }}
          </VChip>
        </template>
        <template #item.timestamp="{ item }">
          {{ formatTime(item.timestamp) }}
        </template>
        <template #item.status="{ item }">
          <div v-if="item.status == '0'">
            {{ t('NetworkDeviceDetail.Pending') }}
          </div>
          <div v-if="item.status == '1'">
            {{ t('NetworkDeviceDetail.Ignored') }}
          </div>
          <div v-if="item.status == '2'">
            {{ t('NetworkDeviceDetail.Processed') }}
          </div>
        </template>
        <template #item.detail="{ item }">
          <VBtn
            v-if="item.status == '0'"
            variant="text"
            color="primary"
            @click="markResolved(item)"
          >
            {{ t('NetworkDeviceDetail.MarkAsResolved') }}
          </VBtn>
          <VBtn
            variant="text"
            color="primary"
            @click="markIgnored(item)"
          >
            {{ t('NetworkDeviceDetail.Ignore') }}
          </VBtn>
          <VBtn
            variant="text"
            color="primary"
            @click="showDetail(item)"
          >
            {{ t('NetworkDeviceDetail.Detail') }}
          </VBtn>
        </template>
        <template #bottom>
          <TablePagination
            v-model:page="page"
            v-model:items-per-page="itemsPerPage"
            :total-items="totalProduct"
            :show-meta="true"
          />
        </template>
      </VDataTableServer>
    </VCard>
    <!-- 事件详情 -->
    <VNavigationDrawer
      v-if="drawer"
      v-model="drawer"
      persistent
      temporary
      location="right"
      width="560"
    >
      <div class="h-screen d-flex flex-column">
        <div class="flex-shrink-0	 d-flex align-center justify-space-between pa-4">
          <div class="text-h5">
            {{ t('NetworkDeviceDetail.EventDetails') }}
          </div>
          <VBtn
            icon
            variant="text"
            color="medium-emphasis"
            size="small"
            @click="drawer = false"
          >
            <VIcon
              icon="tabler-x"
              color="high-emphasis"
              size="24"
            />
          </VBtn>
        </div>

        <VDivider />

        <PerfectScrollbar
          class="pa-4 flex-1-0"
          tag="ul"
          :options="{ wheelPropagation: false }"
        >
          <div class="text-h5 mb-4 d-flex">
            {{ deviceInfo.model }}
          </div>
          <div class="bg-grey-light pa-4 mb-4 rounded">
            <div class="text-button mb-2">
              {{ t('NetworkDeviceDetail.BasicInfo') }}
            </div>
            <VRow>
              <VCol>
                <div class="label">
                  {{ t('NetworkDeviceDetail.Type') }}
                </div>
                <div class="value">
                  <div v-if="eventDetail.type == '0'">
                    {{ t('NetworkDeviceDetail.DeviceAccess') }}
                  </div>
                  <div v-if="eventDetail.type == '1'">
                    {{ t('NetworkDeviceDetail.DevicePerformance') }}
                  </div>
                </div>
              </VCol>
              <VCol>
                <div class="label">
                  {{ t('NetworkDeviceDetail.Level') }}
                </div>
                <div class="value">
                  <VChip
                    variant="outlined"
                    :color="eventClass[getEventIndex(eventDetail)]"
                  >
                    {{ getEventName(eventDetail) }}
                  </VChip>
                </div>
              </VCol>
              <VCol>
                <div class="label">
                  {{ t('NetworkDeviceDetail.OccurrenceTime') }}
                </div>
                <div class="value">
                  {{ formatTime(eventDetail.timestamp) }}
                </div>
              </VCol>
            </VRow>
            <VRow>
              <VCol>
                <div class="label">
                  {{ t('NetworkDeviceDetail.EventDescription') }}
                </div>
                <div v-if="eventDetail.event_name == '0'">
                  {{ t('NetworkDeviceDetail.APOnline') }}
                </div>
                <div v-if="eventDetail.event_name == '1'">
                  {{ t('NetworkDeviceDetail.APOffline') }}
                </div>
                <div v-if="eventDetail.event_name == '3'">
                  {{ t('NetworkDeviceDetail.APHighTemp') }}
                </div>
              </VCol>
            </VRow>
          </div>

          <div class="bg-grey-light pa-4 mb-4 rounded">
            <div class="text-button mb-2">
              {{ t('NetworkDeviceDetail.DeviceInfo') }}
            </div>
            <VRow>
              <VCol>
                <div class="label">
                  {{ t('NetworkEventDetail.DeviceName') }}
                </div>
                <div class="value">
                  {{ deviceInfo.usr_name }}
                </div>
              </VCol>
              <VCol>
                <div class="label">
                  {{ t('NetworkDeviceDetail.Status') }}
                </div>
                <div
                  v-if="deviceInfo.onoff == 'online'"
                  class="value text-success"
                >
                  {{ t('NetworkDeviceDetail.Online') }}
                </div>
                <div
                  v-else
                  class="value text-error"
                >
                  {{ t('NetworkDeviceDetail.Offline') }}
                </div>
              </VCol>
              <VCol>
                <div class="label">
                  {{ t('NetworkDeviceDetail.Version') }}
                </div>
                <div class="value">
                  {{ deviceInfo.version }}
                </div>
              </VCol>
            </VRow>
            <VRow>
              <VCol>
                <div class="label">
                  {{ t('NetworkDeviceDetail.IPAddress') }}
                </div>
                <div class="value">
                  {{ deviceInfo.ip }}
                </div>
              </VCol>
              <VCol>
                <div class="label">
                  {{ t('NetworkDeviceDetail.MACAddress') }}
                </div>
                <div class="value">
                  {{ deviceInfo.mac }}
                </div>
              </VCol>
              <VCol />
            </VRow>
          </div>

          <div class="rounded pa-4 border border-sm">
            <div class="text-button mb-2">
              {{ t('NetworkDeviceDetail.Maintenance') }}
            </div>
            <div class="text-on-surface opacity-60 text-body-1">
              {{ t('NetworkDeviceDetail.MaintenanceDesc') }}
            </div>
          </div>
        </PerfectScrollbar>
        <VDivider />
        <div class="flex-shrink-0	 pa-4 d-flex align-center justify-end ">
          <VBtn
            class="mr-4"
            color="secondary"
            variant="tonal"
            @click="drawer = false"
          >
            {{ t('NetworkDeviceDetail.Cancel') }}
          </VBtn>
          <VBtn
            color="primary"
            @click="markResolved(eventDetail)"
          >
            {{ t('NetworkDeviceDetail.MarkResolved') }}
          </VBtn>
        </div>
      </div>
    </VNavigationDrawer>

    <!-- 已设置不再提醒的事件 -->
    <VDialog
      v-model="isDialogVisible"
      width="1000"
    >
      <DialogCloseBtn @click="isDialogVisible = !isDialogVisible" />
      <VCard class="pa-8">
        <template #title>
          <div class="text-center text-h4">
            {{ t('NetworkDeviceDetail.NoMoreRemindEvents') }}
          </div>
        </template>
        <VDataTableServer
          class="mb-4"
          :items="ignoreList"
          :headers="noMoreRemindHeaders"
          :items-length="totalProduct"
          hide-default-footer
          :no-data-text="t('NoData')"
        >
          <template #item.no="{ index }">
            <span>{{ index + 1 }}</span>
          </template>
          <template #item.handle="{ item }">
            <VBtn
              variant="text"
              color="error"
              @click="markIgnoredChange(item)"
            >
              {{ t('NetworkDeviceDetail.Remove') }}
            </VBtn>
          </template>
        </VDataTableServer>
      </VCard>
    </VDialog>
  </div>
</template>

<style lang="scss" scoped>
.device-detail {
  .label {
    color: #999;
    font-size: 13px;
    margin-block-end: 2px;
  }

  .value {
    color: rgba($color: var(--v-theme-on-surface), $alpha: 90%);
    font-size: 15px;
  }

  .sub-title {
    color: rgba($color: var(--v-theme-on-surface), $alpha: 55%);
    font-size: 15px;
    font-weight: normal;
  }

  .rounded-progress {
    border-end-start-radius: 10px !important;
    border-start-start-radius: 10px !important;
  }
}
</style>

<style lang="scss">
.custom-area-chart {
  .apexcharts-legend {
    &-series {
      border: 1px solid rgba($color: var(--v-theme-on-surface), $alpha: 12%);
      border-radius: 6px;
      padding-block: 3px;
      padding-inline: 15px;
    }
  }
}
</style>
